  import React from 'react';

  // Komponen untuk ikon media sosial
  const SocialIcon = ({ href, children }) => (
    <a href={href} target="_blank" rel="noopener noreferrer" className="text-gray-500 hover:text-gray-700 transition-colors duration-200">
      <span className="sr-only">{children.type.name}</span>
      {children}
    </a>
  );

  const Footer = () => {
    return (
      <footer className="bg-white border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-6">
          
          {/* Konten utama footer */}
          <div className="py-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
              
              {/* Logo dan deskripsi singkat */}
              <div className="text-center md:text-left">
                <h2 className="text-xl font-semibold text-gray-900 mb-2"><PERSON><PERSON></h2>
                <p className="text-sm text-gray-600 max-w-sm leading-relaxed">
                  Platform pemetaan talenta berbasis AI untuk mengembangkan potensi siswa melalui analisis data yang akurat.
                </p>
              </div>

              {/* Navigasi minimal */}
              <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-8">
                <nav className="flex space-x-6">
                  <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">Tentang</a>
                  <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">Kontak</a>
                  <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">Privasi</a>
                </nav>
                
                {/* Media sosial */}
                <div className="flex space-x-4">
                  <SocialIcon href="#">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    </svg>
                  </SocialIcon>
                  <SocialIcon href="#">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.71v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                  </SocialIcon>
                  <SocialIcon href="#">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </SocialIcon>
                </div>
              </div>

            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-gray-100 py-4 text-center">
            <p className="text-xs text-gray-500">
              © {new Date().getFullYear()} Peta Talenta. All rights reserved.
            </p>
          </div>

        </div>
      </footer>
    );
  };

  export default Footer;